import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, Heart, Users, BookOpen, Music, Coffee, Baby } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { RotatingText, SlideUpText, GradientRotatingText } from "@/components/rotating-text"

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative text-white py-20 lg:py-32 overflow-hidden">
        {/* Background Image with Next.js optimization */}
        <Image
          src="/images/hero/church-worship-interior.jpg"
          alt="Beautiful church interior with stained glass windows and warm sunlight"
          fill
          className="object-cover"
          priority
          quality={75}
          sizes="100vw"
        />
        {/* Overlay for text readability */}
        <div className="absolute inset-0 bg-black/40" />
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 hero-title">
              <RotatingText
                texts={[
                  "Welcome to Grace Community Church",
                  "Find Your Home at Grace Community",
                  "Join Our Church Family Today",
                  "Experience God's Love Here"
                ]}
                interval={4000}
                animationType="fade"
                className="block animate-text-glow"
              />
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 hero-subtitle">
              <SlideUpText
                texts={[
                  "A place where faith, hope, and love come together",
                  "Where every person matters to God",
                  "Building community through Christ's love",
                  "Growing in faith, serving with purpose",
                  "Your spiritual journey starts here"
                ]}
                interval={3500}
                className="block min-h-[3rem] md:min-h-[2rem]"
              />
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up" style={{ animationDelay: "0.8s", animationFillMode: "both" }}>
              <Button size="lg" className="bg-white text-blue-900 hover:bg-blue-50 hover:scale-105 transition-all duration-300 hover:shadow-lg">
                Plan Your Visit
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-900 hover:scale-105 transition-all duration-300 hover:shadow-lg"
              >
                Watch Online
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Service Times */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              <RotatingText
                texts={[
                  "Join Us This Sunday",
                  "Worship With Us This Week",
                  "Come As You Are",
                  "Experience God's Presence"
                ]}
                interval={5000}
                animationType="fade"
              />
            </h2>
            <p className="text-lg text-gray-600">
              <RotatingText
                texts={[
                  "Everyone is welcome at Grace Community Church",
                  "No matter where you are in your faith journey",
                  "Come and experience God's love and grace",
                  "Find community, purpose, and belonging here"
                ]}
                interval={4500}
                animationType="fade"
              />
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <Card className="text-center">
              <CardHeader>
                <Clock className="w-12 h-12 mx-auto text-blue-600 mb-4" />
                <CardTitle>Sunday Services</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-semibold">9:00 AM - Traditional Service</p>
                  <p className="font-semibold">11:00 AM - Contemporary Service</p>
                  <p className="text-sm text-gray-600">Childcare available for all services</p>
                </div>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <MapPin className="w-12 h-12 mx-auto text-blue-600 mb-4" />
                <CardTitle>Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-semibold">123 Faith Street</p>
                  <p className="font-semibold">Springfield, IL 62701</p>
                  <Button variant="outline" size="sm" className="mt-2">
                    Get Directions
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Users className="w-12 h-12 mx-auto text-blue-600 mb-4" />
                <CardTitle>What to Expect</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm">Casual dress welcome</p>
                  <p className="text-sm">Free coffee & donuts</p>
                  <p className="text-sm">Kids programs available</p>
                  <p className="text-sm">Friendly, welcoming community</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* About Preview */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-lg text-gray-700 mb-6">
                At Grace Community Church, we believe in creating a welcoming environment where people can encounter
                God, grow in their faith, and serve their community. We are committed to sharing the love of Christ
                through worship, fellowship, and outreach.
              </p>
              <div className="grid sm:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center gap-3">
                  <Heart className="w-6 h-6 text-red-500" />
                  <span className="font-semibold">Love God</span>
                </div>
                <div className="flex items-center gap-3">
                  <Users className="w-6 h-6 text-blue-500" />
                  <span className="font-semibold">Love People</span>
                </div>
                <div className="flex items-center gap-3">
                  <BookOpen className="w-6 h-6 text-green-500" />
                  <span className="font-semibold">Grow in Faith</span>
                </div>
                <div className="flex items-center gap-3">
                  <Coffee className="w-6 h-6 text-orange-500" />
                  <span className="font-semibold">Serve Others</span>
                </div>
              </div>
              <Link href="/about">
                <Button>Learn More About Us</Button>
              </Link>
            </div>
            <div className="relative">
              <Image
                src="/images/about-church-community.jpg"
                alt="Church community gathering in fellowship"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Ministries Preview */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              <GradientRotatingText
                texts={[
                  "Get Connected",
                  "Find Your Ministry",
                  "Discover Your Gifts",
                  "Serve Together"
                ]}
                interval={4000}
                gradientColors="from-blue-600 via-purple-600 to-blue-800"
              />
            </h2>
            <p className="text-lg text-gray-600">
              <RotatingText
                texts={[
                  "Find your place in our church family",
                  "Discover how God wants to use your gifts",
                  "Connect with others who share your passions",
                  "Make a difference in your community"
                ]}
                interval={3800}
                animationType="slide"
              />
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Baby className="w-12 h-12 mx-auto text-pink-500 mb-2" />
                <CardTitle className="text-lg">Children's Ministry</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">Ages 0-12, Sunday School, VBS, and special events</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Users className="w-12 h-12 mx-auto text-blue-500 mb-2" />
                <CardTitle className="text-lg">Youth Group</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">Ages 13-18, Wednesday nights and weekend activities</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Music className="w-12 h-12 mx-auto text-purple-500 mb-2" />
                <CardTitle className="text-lg">Worship Team</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">Join our music ministry and lead worship</p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <BookOpen className="w-12 h-12 mx-auto text-green-500 mb-2" />
                <CardTitle className="text-lg">Small Groups</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">Bible study and fellowship groups throughout the week</p>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-8">
            <Link href="/ministries">
              <Button variant="outline">View All Ministries</Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Upcoming Events */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Upcoming Events</h2>
            <p className="text-lg text-gray-600">Join us for these special occasions</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            <Card>
              <CardHeader>
                <Badge className="w-fit mb-2">This Sunday</Badge>
                <CardTitle>Easter Celebration Service</CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  March 31, 2024 at 9:00 AM & 11:00 AM
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Join us for a special Easter celebration with music, testimonies, and the message of hope.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Badge variant="secondary" className="w-fit mb-2">
                  Next Week
                </Badge>
                <CardTitle>Community Food Drive</CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  April 6, 2024 at 10:00 AM
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Help us serve our community by donating non-perishable food items.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Badge variant="outline" className="w-fit mb-2">
                  April 13
                </Badge>
                <CardTitle>Youth Spring Retreat</CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  April 13-15, 2024
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  A weekend retreat for our youth group with activities, worship, and fellowship.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-8">
            <Link href="/events">
              <Button>View All Events</Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="relative py-16 text-white overflow-hidden">
        {/* Background Image with Next.js optimization */}
        <Image
          src="/images/hero/church-worship-interior.jpg"
          alt="Beautiful church interior with stained glass windows and warm sunlight"
          fill
          className="object-cover"
          quality={60}
          sizes="100vw"
        />
        {/* Overlay for text readability */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-purple-900/80" />
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Take Your Next Step?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Whether you're new to faith or looking to grow deeper, we're here to support you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
                Get in Touch
              </Button>
            </Link>
            <Link href="/give">
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                Give Online
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
