"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu, Church } from "lucide-react"

export function Header() {
  const [isOpen, setIsOpen] = useState(false)

  const navigation = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Ministries", href: "/ministries" },
    { name: "Events", href: "/events" },
    { name: "Sermons", href: "/sermons" },
    { name: "Contact", href: "/contact" },
  ]

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo with hover effects */}
          <Link href="/" className="flex items-center space-x-2 group transition-all duration-300 hover:scale-105">
            <Church className="h-8 w-8 text-blue-600 transition-all duration-300 group-hover:text-blue-700 group-hover:rotate-12" />
            <span className="font-bold text-xl text-gray-900 transition-colors duration-300 group-hover:text-blue-600">Grace Community</span>
          </Link>

          {/* Desktop Navigation with enhanced hover effects */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="relative text-gray-700 hover:text-blue-600 font-medium transition-all duration-300 group py-2 px-1"
              >
                <span className="relative z-10 transition-transform duration-300 group-hover:scale-110">
                  {item.name}
                </span>
                {/* Animated underline */}
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300 group-hover:w-full"></span>
                {/* Subtle background glow on hover */}
                <span className="absolute inset-0 bg-blue-50 rounded-lg opacity-0 transition-opacity duration-300 group-hover:opacity-100 -z-10"></span>
              </Link>
            ))}
          </nav>

          {/* Desktop CTA */}
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/give">
              <Button variant="outline">Give</Button>
            </Link>
            <Link href="/visit">
              <Button>Plan Visit</Button>
            </Link>
          </div>

          {/* Mobile Menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-8">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="text-lg font-medium text-gray-700 hover:text-blue-600 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
                <div className="pt-4 space-y-2">
                  <Link href="/give" onClick={() => setIsOpen(false)}>
                    <Button variant="outline" className="w-full">
                      Give
                    </Button>
                  </Link>
                  <Link href="/visit" onClick={() => setIsOpen(false)}>
                    <Button className="w-full">Plan Visit</Button>
                  </Link>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
